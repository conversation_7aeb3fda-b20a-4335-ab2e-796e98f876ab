package utils

import (
	"context"
	"encoding/json"
	"fmt"
	"resty.dev/v3"
	"time"
)

// HTTPClient HTTP客户端配置
type HTTPClient struct {
	client  *resty.Client
	baseURL string
	timeout time.Duration
	headers map[string]string
}

// HTTPClientOption HTTP客户端配置选项
type HTTPClientOption func(*HTTPClient)

// HTTPResponse HTTP响应结构
type HTTPResponse struct {
	StatusCode int
	Body       []byte
	Headers    map[string]string
	Error      error
}

// PostRequest POST请求参数
type PostRequest struct {
	URL         string
	Body        interface{}
	Headers     map[string]string
	ContentType string
	Timeout     time.Duration
}

// NewHTTPClient 创建新的HTTP客户端
func NewHTTPClient(options ...HTTPClientOption) *HTTPClient {
	client := &HTTPClient{
		client:  resty.New(),
		timeout: 30 * time.Second,
		headers: make(map[string]string),
	}

	// 应用配置选项
	for _, option := range options {
		option(client)
	}

	// 设置基础配置
	client.client.SetTimeout(client.timeout)
	if client.baseURL != "" {
		client.client.SetBaseURL(client.baseURL)
	}

	// 设置默认请求头
	for key, value := range client.headers {
		client.client.SetHeader(key, value)
	}

	return client
}

// WithBaseURL 设置基础URL
func WithBaseURL(baseURL string) HTTPClientOption {
	return func(c *HTTPClient) {
		c.baseURL = baseURL
	}
}

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) HTTPClientOption {
	return func(c *HTTPClient) {
		c.timeout = timeout
	}
}

// WithHeaders 设置默认请求头
func WithHeaders(headers map[string]string) HTTPClientOption {
	return func(c *HTTPClient) {
		for key, value := range headers {
			c.headers[key] = value
		}
	}
}

// WithRetry 设置重试配置
func WithRetry(count int, waitTime time.Duration) HTTPClientOption {
	return func(c *HTTPClient) {
		c.client.SetRetryCount(count)
		c.client.SetRetryWaitTime(waitTime)
	}
}

// Post 发送POST请求
func (c *HTTPClient) Post(req PostRequest) (*HTTPResponse, error) {
	request := c.client.R()

	// 设置请求头
	if req.Headers != nil {
		for key, value := range req.Headers {
			request.SetHeader(key, value)
		}
	}

	// 设置Content-Type
	if req.ContentType != "" {
		request.SetHeader("Content-Type", req.ContentType)
	}

	// 设置请求体
	if req.Body != nil {
		switch body := req.Body.(type) {
		case string:
			request.SetBody(body)
		case []byte:
			request.SetBody(body)
		case map[string]interface{}:
			request.SetBody(body)
		default:
			// 尝试JSON序列化
			jsonData, err := json.Marshal(body)
			if err != nil {
				return nil, fmt.Errorf("序列化请求体失败: %w", err)
			}
			request.SetBody(jsonData)
			if req.ContentType == "" {
				request.SetHeader("Content-Type", "application/json")
			}
		}
	}

	// 设置超时
	if req.Timeout > 0 {
		request.SetTimeout(req.Timeout)
	}

	// 发送请求
	resp, err := request.Post(req.URL)
	if err != nil {
		return &HTTPResponse{
			Error: fmt.Errorf("发送POST请求失败: %w", err),
		}, err
	}

	// 构建响应
	headers := make(map[string]string)
	for key, values := range resp.Header() {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	return &HTTPResponse{
		StatusCode: resp.StatusCode(),
		Body:       resp.Body(),
		Headers:    headers,
		Error:      nil,
	}, nil
}

// PostJSON 发送JSON格式的POST请求
func (c *HTTPClient) PostJSON(url string, body interface{}, headers ...map[string]string) (*HTTPResponse, error) {
	req := PostRequest{
		URL:         url,
		Body:        body,
		ContentType: "application/json",
	}

	if len(headers) > 0 {
		req.Headers = headers[0]
	}

	return c.Post(req)
}

// PostForm 发送表单格式的POST请求
func (c *HTTPClient) PostForm(url string, formData map[string]string, headers ...map[string]string) (*HTTPResponse, error) {
	req := PostRequest{
		URL:         url,
		Body:        formData,
		ContentType: "application/x-www-form-urlencoded",
	}

	if len(headers) > 0 {
		req.Headers = headers[0]
	}

	return c.Post(req)
}

// PostWithContext 带上下文的POST请求
func (c *HTTPClient) PostWithContext(ctx context.Context, req PostRequest) (*HTTPResponse, error) {
	request := c.client.R().SetContext(ctx)

	// 设置请求头
	if req.Headers != nil {
		for key, value := range req.Headers {
			request.SetHeader(key, value)
		}
	}

	// 设置Content-Type
	if req.ContentType != "" {
		request.SetHeader("Content-Type", req.ContentType)
	}

	// 设置请求体
	if req.Body != nil {
		switch body := req.Body.(type) {
		case string:
			request.SetBody(body)
		case []byte:
			request.SetBody(body)
		case map[string]interface{}:
			request.SetBody(body)
		default:
			// 尝试JSON序列化
			jsonData, err := json.Marshal(body)
			if err != nil {
				return nil, fmt.Errorf("序列化请求体失败: %w", err)
			}
			request.SetBody(jsonData)
			if req.ContentType == "" {
				request.SetHeader("Content-Type", "application/json")
			}
		}
	}

	// 发送请求
	resp, err := request.Post(req.URL)
	if err != nil {
		return &HTTPResponse{
			Error: fmt.Errorf("发送POST请求失败: %w", err),
		}, err
	}

	// 构建响应
	headers := make(map[string]string)
	for key, values := range resp.Header() {
		if len(values) > 0 {
			headers[key] = values[0]
		}
	}

	return &HTTPResponse{
		StatusCode: resp.StatusCode(),
		Body:       resp.Body(),
		Headers:    headers,
		Error:      nil,
	}, nil
}

// 全局默认HTTP客户端
var defaultHTTPClient = NewHTTPClient(
	WithTimeout(30*time.Second),
	WithRetry(3, 1*time.Second),
)

// Post 使用默认客户端发送POST请求
func Post(url string, body interface{}, headers ...map[string]string) (*HTTPResponse, error) {
	req := PostRequest{
		URL:  url,
		Body: body,
	}

	if len(headers) > 0 {
		req.Headers = headers[0]
	}

	return defaultHTTPClient.Post(req)
}

// PostJSON 使用默认客户端发送JSON格式的POST请求
func PostJSON(url string, body interface{}, headers ...map[string]string) (*HTTPResponse, error) {
	return defaultHTTPClient.PostJSON(url, body, headers...)
}

// PostForm 使用默认客户端发送表单格式的POST请求
func PostForm(url string, formData map[string]string, headers ...map[string]string) (*HTTPResponse, error) {
	return defaultHTTPClient.PostForm(url, formData, headers...)
}

// PostWithTimeout 使用默认客户端发送带超时的POST请求
func PostWithTimeout(url string, body interface{}, timeout time.Duration, headers ...map[string]string) (*HTTPResponse, error) {
	req := PostRequest{
		URL:     url,
		Body:    body,
		Timeout: timeout,
	}

	if len(headers) > 0 {
		req.Headers = headers[0]
	}

	return defaultHTTPClient.Post(req)
}

// ParseJSONResponse 解析JSON响应
func (r *HTTPResponse) ParseJSONResponse(result interface{}) error {
	if r.Error != nil {
		return r.Error
	}

	if r.StatusCode < 200 || r.StatusCode >= 300 {
		return fmt.Errorf("HTTP请求失败，状态码: %d, 响应: %s", r.StatusCode, string(r.Body))
	}

	if err := json.Unmarshal(r.Body, result); err != nil {
		return fmt.Errorf("解析JSON响应失败: %w", err)
	}

	return nil
}

// IsSuccess 检查响应是否成功
func (r *HTTPResponse) IsSuccess() bool {
	return r.Error == nil && r.StatusCode >= 200 && r.StatusCode < 300
}

// GetBodyString 获取响应体字符串
func (r *HTTPResponse) GetBodyString() string {
	return string(r.Body)
}
