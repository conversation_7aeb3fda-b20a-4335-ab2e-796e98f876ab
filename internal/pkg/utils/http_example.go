package utils

import (
	"context"
	"fmt"
	"time"
)

// HTTPExample HTTP使用示例
type HTTPExample struct{}

// ExampleBasicPost 基础POST请求示例
func (e *HTTPExample) ExampleBasicPost() {
	// 1. 使用全局默认客户端发送JSON请求
	response, err := PostJSON("https://api.example.com/users", map[string]interface{}{
		"name":  "张三",
		"email": "<EMAIL>",
		"age":   25,
	})

	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	if response.IsSuccess() {
		fmt.Printf("请求成功，状态码: %d\n", response.StatusCode)
		fmt.Printf("响应内容: %s\n", response.GetBodyString())
	} else {
		fmt.Printf("请求失败，状态码: %d\n", response.StatusCode)
	}
}

// ExamplePostWithHeaders 带请求头的POST请求示例
func (e *HTTPExample) ExamplePostWithHeaders() {
	headers := map[string]string{
		"Authorization": "Bearer your-token-here",
		"User-Agent":    "MyApp/1.0",
	}

	response, err := PostJSON("https://api.example.com/data", map[string]string{
		"message": "Hello World",
	}, headers)

	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	// 解析JSON响应
	var result map[string]interface{}
	if err := response.ParseJSONResponse(&result); err != nil {
		fmt.Printf("解析响应失败: %v\n", err)
		return
	}

	fmt.Printf("解析后的响应: %+v\n", result)
}

// ExamplePostForm 表单POST请求示例
func (e *HTTPExample) ExamplePostForm() {
	formData := map[string]string{
		"username": "testuser",
		"password": "testpass",
	}

	response, err := PostForm("https://api.example.com/login", formData)
	if err != nil {
		fmt.Printf("登录请求失败: %v\n", err)
		return
	}

	fmt.Printf("登录响应: %s\n", response.GetBodyString())
}

// ExampleCustomClient 自定义客户端示例
func (e *HTTPExample) ExampleCustomClient() {
	// 创建自定义HTTP客户端
	client := NewHTTPClient(
		WithBaseURL("https://api.example.com"),
		WithTimeout(10*time.Second),
		WithHeaders(map[string]string{
			"Authorization": "Bearer your-token",
			"Content-Type":  "application/json",
		}),
		WithRetry(3, 2*time.Second),
	)

	// 使用自定义客户端发送请求
	response, err := client.PostJSON("/users", map[string]interface{}{
		"name": "李四",
		"age":  30,
	})

	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	fmt.Printf("响应状态码: %d\n", response.StatusCode)
	fmt.Printf("响应内容: %s\n", response.GetBodyString())
}

// ExampleWithContext 带上下文的请求示例
func (e *HTTPExample) ExampleWithContext() {
	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	client := NewHTTPClient()

	req := PostRequest{
		URL: "https://api.example.com/slow-endpoint",
		Body: map[string]string{
			"data": "some data",
		},
		ContentType: "application/json",
	}

	response, err := client.PostWithContext(ctx, req)
	if err != nil {
		fmt.Printf("请求失败: %v\n", err)
		return
	}

	fmt.Printf("响应: %s\n", response.GetBodyString())
}

// ExampleErrorHandling 错误处理示例
func (e *HTTPExample) ExampleErrorHandling() {
	response, err := PostJSON("https://api.example.com/invalid-endpoint", map[string]string{
		"test": "data",
	})

	if err != nil {
		fmt.Printf("网络错误: %v\n", err)
		return
	}

	if !response.IsSuccess() {
		fmt.Printf("HTTP错误 - 状态码: %d, 响应: %s\n", 
			response.StatusCode, response.GetBodyString())
		return
	}

	// 尝试解析响应
	var result map[string]interface{}
	if err := response.ParseJSONResponse(&result); err != nil {
		fmt.Printf("JSON解析错误: %v\n", err)
		return
	}

	fmt.Printf("成功: %+v\n", result)
}

// ExampleWithTimeout 带超时的请求示例
func (e *HTTPExample) ExampleWithTimeout() {
	// 发送带5秒超时的请求
	response, err := PostWithTimeout(
		"https://api.example.com/data",
		map[string]string{"key": "value"},
		5*time.Second,
	)

	if err != nil {
		fmt.Printf("请求超时或失败: %v\n", err)
		return
	}

	fmt.Printf("响应: %s\n", response.GetBodyString())
}
